{"version": "1.23.11", "setting": [{"id": "1", "value": "10", "key": "database_version", "type": ""}, {"id": "2", "value": "{\"patch-setting-value-type.sql\":true,\"patch-improve-performance.sql\":true,\"patch-2fa.sql\":true,\"patch-add-retry-interval-monitor.sql\":true,\"patch-incident-table.sql\":true,\"patch-group-table.sql\":true,\"patch-monitor-push_token.sql\":true,\"patch-http-monitor-method-body-and-headers.sql\":true,\"patch-2fa-invalidate-used-token.sql\":true,\"patch-notification_sent_history.sql\":true,\"patch-monitor-basic-auth.sql\":true,\"patch-add-docker-columns.sql\":true,\"patch-status-page.sql\":true,\"patch-proxy.sql\":true,\"patch-monitor-expiry-notification.sql\":true,\"patch-status-page-footer-css.sql\":true,\"patch-added-mqtt-monitor.sql\":true,\"patch-add-clickable-status-page-link.sql\":true,\"patch-add-sqlserver-monitor.sql\":true,\"patch-add-other-auth.sql\":true,\"patch-grpc-monitor.sql\":true,\"patch-add-radius-monitor.sql\":true,\"patch-monitor-add-resend-interval.sql\":true,\"patch-ping-packet-size.sql\":true,\"patch-maintenance-table2.sql\":true,\"patch-add-gamedig-monitor.sql\":true,\"patch-add-google-analytics-status-page-tag.sql\":true,\"patch-http-body-encoding.sql\":true,\"patch-add-description-monitor.sql\":true,\"patch-api-key-table.sql\":true,\"patch-monitor-tls.sql\":true,\"patch-maintenance-cron.sql\":true,\"patch-add-parent-monitor.sql\":true,\"patch-add-invert-keyword.sql\":true,\"patch-added-json-query.sql\":true,\"patch-added-kafka-producer.sql\":true,\"patch-add-certificate-expiry-status-page.sql\":true,\"patch-monitor-oauth-cc.sql\":true,\"patch-add-timeout-monitor.sql\":true,\"patch-add-gamedig-given-port.sql\":true,\"patch-notification-config.sql\":true,\"patch-fix-kafka-producer-booleans.sql\":true,\"patch-timeout.sql\":true,\"patch-monitor-tls-info-add-fk.sql\":true}", "key": "databasePatchedFiles", "type": ""}, {"id": "3", "value": "$2a$10$iV8u8nGUTT5z0TIty86W9OwSlQ7T5b1V/66Q225eUet1AGk21c/u2", "key": "jwtSecret", "type": ""}, {"id": "4", "value": "true", "key": "initServerTimezone", "type": ""}, {"id": "5", "value": "\"Asia/Shanghai\"", "key": "serverTimezone", "type": "general"}, {"id": "6", "value": "false", "key": "checkUpdate", "type": "general"}, {"id": "7", "value": "false", "key": "searchEngineIndex", "type": "general"}, {"id": "8", "value": "\"dashboard\"", "key": "entryPage", "type": "general"}, {"id": "9", "value": "true", "key": "nscd", "type": "general"}, {"id": "10", "value": "false", "key": "dnsCache", "type": "general"}, {"id": "11", "value": "1", "key": "keepDataPeriodDays", "type": "general"}, {"id": "12", "value": "[7,14,21]", "key": "tlsExpiryNotifyDays", "type": "general"}, {"id": "13", "value": "false", "key": "trustProxy", "type": "general"}], "tag": [], "datetime": "2025-07-14 22:05:52", "notification": [], "monitor": [{"method": "GET", "weight": "2000", "maxredirects": "10", "id": "1", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "halo鍗氬", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-01 03:42:33", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-olah.hf.space/", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "2", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "瀹瑰櫒淇濇椿", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "600", "created_date": "2025-04-01 06:07:13", "upside_down": false, "retry_interval": "60", "basic_auth_pass": "", "push_token": "", "url": "https://", "dns_resolve_type": "A", "type": "group", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "3", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "alist", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-01 06:07:13", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-tsila.hf.space", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "4", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "gemini-balance", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-01 06:08:06", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-gemini-balance.hf.space/", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "5", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "uptime-kuma", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-01 06:09:30", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-amuk2.hf.space", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "6", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "koyeb-Shellngn", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "598", "created_date": "2025-04-01 11:56:21", "upside_down": false, "retry_interval": "598", "basic_auth_pass": "", "push_token": "", "url": "https://changing-marketa-gally-e5dd5535.koyeb.app/", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": false}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "7", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "koyeb-Shellngn", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "599", "created_date": "2025-04-01 11:58:12", "upside_down": false, "retry_interval": "599", "basic_auth_pass": "", "push_token": "", "url": "https://ssh.gally.cloudns.ph/", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "8", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "fly.io-easynode", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-01 11:59:21", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://easynode.fly.dev", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "9", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "render-grok", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-01 12:18:05", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://grok-gateway-latest-p1os.onrender.com", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "10", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "Hugging Face", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "600", "created_date": "2025-04-01 12:18:53", "upside_down": false, "retry_interval": "60", "basic_auth_pass": "", "push_token": "", "url": "https://", "dns_resolve_type": "A", "type": "group", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "11", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "hax-1panel", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-01 12:32:05", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://hax.gally.cloudns.nz:2052/", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "12", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "claw-grok", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-01 13:30:38", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "http://grok.gally.ip-ddns.com/admin", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "13", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "openwen<PERSON>", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-01 14:18:16", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-web.hf.space", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "14", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "LibreTV", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-02 04:47:47", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://tv.gally.ddns-ip.net", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "15", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "deno-grok", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-02 05:21:56", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://grok-gally.deno.dev/", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "16", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "docker.gally.ddns-ip.net", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "600", "created_date": "2025-04-02 14:11:16", "upside_down": false, "retry_interval": "600", "basic_auth_pass": "", "push_token": "", "url": "https://docker.gally.ddns-ip.net/search", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "17", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "docker.gally.cloudns.nz", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-02 14:17:15", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://docker.gally.cloudns.nz/search", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "18", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "docker.gally.cloudns.ph", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-02 14:17:32", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://docker.gally.cloudns.ph/search", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "19", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "news", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-02 15:04:28", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://newsnow-ahm.pages.dev/", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "20", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "render-chatapi", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-03 13:54:38", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://chat-api-khsk.onrender.com/", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "POST", "weight": "2000", "maxredirects": "10", "id": "21", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": null, "name": "grok", "description": null, "headers": null, "accepted_statuscodes_json": "[\"200-299\"]", "body": "Picgo锛歝hv_Ezsm_0093e1f849712ab719d195141f2cc9c23a68245cebbb62c67b3d4b66d169103f01d2d55c8268681fa04d6eaa31e62b694b79ea9f38cdb315affc7c4e34ee8a41", "interval": "600", "created_date": "2025-04-04 01:28:10", "upside_down": false, "retry_interval": "600", "basic_auth_pass": null, "push_token": "", "url": "https://gallyga-grok.hf.space", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": null, "hostname": "", "active": false}, {"method": "", "weight": "0", "maxredirects": "", "id": "CF_CLEARANCE锛歠YibYU2Hw7OgRDuIcTUSvlbEKfGlAV8DvL0kQENxIbQ-1743740605-*******-vMEAHy5kQL5pIvtp2Fmzkx0aSitt9optcSfENjN7GkyQx16PyM9l26vrJ6sNmUZdBv1Mm8U9Yrs5J2Ljqbn4keuGV9ok6X_2EZkjlFxZlymEJWioOn2ojKI_lBKgUVrVqlMyQPtGWlwoytWuOxVHe.LuaU7nhVaEJ2Alv4oj5iw_krBdM9iFIILWYBwpe5F6bGXuXwjugmh6kaUVoRQd1mIFw5MwAaf8O2_JXCP07ePjvjnwTA8BamwMKn1evVC81MKcGbaB90eXO8hVJEX1L7UAJk8vOzb6jZ8T6CDr_xZPJHjJj6H4MMKIboYQJizIlBG871ZPvJ7dR7YjCtM37q.ICWnuI1cZ.m3j72CURR3cYam0cPlfNpKunUuxNmDI", "dns_resolve_server": "", "user_id": "", "port": "", "maxretries": "", "dns_last_result": "", "keyword": "", "timeout": "", "name": "", "description": "", "headers": "", "accepted_statuscodes_json": "", "body": "0", "interval": "", "created_date": "", "upside_down": false, "retry_interval": "", "basic_auth_pass": "", "push_token": "", "url": "", "dns_resolve_type": "", "type": "", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": false}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "22", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "bestv-deno", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-05 08:17:13", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://tv-gally.deno.dev/", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "23", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "bestv-yourware.so", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-05 08:19:18", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://s3jqquvxf1.app.yourware.so/", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "24", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "deno-api鍙嶄唬", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-05 08:42:26", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://fd0.deno.dev/", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "25", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "voapi", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-12 14:41:43", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-ovapi.hf.space/profile", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "26", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "V<PERSON>era", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-13 13:49:50", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-velo.hf.space", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "27", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "<PERSON><PERSON><PERSON>", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-13 13:51:07", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-wenapi.hf.space", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "28", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "<PERSON><PERSON>i", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-13 13:52:11", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-chatipa.hf.space/admin", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "29", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "northflank-grok", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-19 06:07:49", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://grok.gally.ddns-ip.net/admin", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "30", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "NocoDB", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-24 14:23:12", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-ocon.hf.space/dashboard/", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "31", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "N8N", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-24 14:44:38", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-n8.hf.space", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "32", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "sourcegraph", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-24 15:10:23", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-sourcegraph.hf.space", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "33", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "qexo", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-04-28 13:33:29", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-oxeq.hf.space/", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "34", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "trae", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-05-13 13:25:44", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-eatr.hf.space/api/health", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "35", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "qwen", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-05-15 12:53:41", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-qwen2api.hf.space/v1/models", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "36", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "so<PERSON><PERSON><PERSON>", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-05-17 04:04:43", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-soraapi.hf.space/health", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "37", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "genspark", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-05-17 04:25:33", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-genspark.hf.space/v1/models", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "", "weight": "", "maxredirects": "[\"200-299\"]", "id": "38", "dns_resolve_server": "", "user_id": "3600", "port": "2025-05-17 04:31:08", "maxretries": "0", "dns_last_result": "3600", "keyword": "0", "timeout": "", "name": "grok 鐨勫厠闅唡1", "description": "", "headers": "", "accepted_statuscodes_json": "A", "body": "", "interval": "https://gallyga-grok.hf.space", "created_date": "", "upside_down": true, "retry_interval": "", "basic_auth_pass": "", "push_token": "GET", "url": "http", "dns_resolve_type": "*******", "type": "2000", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "39", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "gemini", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-05-17 04:36:24", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-gmn2a.hf.space/v1/models", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "40", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "kilo", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-05-18 04:11:29", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-kilo.hf.space/v1/models", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "41", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "v2-cloudflare", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-05-18 08:53:51", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://v2.gally.cloudns.ph/panel", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "42", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "augment2api", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-06-01 15:10:47", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-augment.hf.space", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "43", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "涓存椂閭绯荤粺", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-06-14 10:32:02", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://mail.gally.cloudns.nz/admin", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "44", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "vaultwarden", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-07-02 04:48:27", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-warden.hf.space/", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}, {"method": "GET", "weight": "2000", "maxredirects": "10", "id": "45", "dns_resolve_server": "*******", "user_id": "1", "port": "", "maxretries": "0", "dns_last_result": "", "keyword": "", "timeout": "", "name": "filen", "description": "", "headers": "", "accepted_statuscodes_json": "[\"200-299\"]", "body": "", "interval": "3600", "created_date": "2025-07-09 11:48:45", "upside_down": false, "retry_interval": "3600", "basic_auth_pass": "", "push_token": "", "url": "https://gallyga-file.hf.space", "dns_resolve_type": "A", "type": "http", "ignore_tls": false, "basic_auth_user": "", "hostname": "", "active": true}]}