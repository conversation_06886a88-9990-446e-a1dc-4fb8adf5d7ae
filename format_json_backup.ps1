# 格式化 JSON 备份文件
$inputFile = "Uptime_Kuma_Backup_2025_07_14-22_07_17.json"
$timestamp = Get-Date -Format "yyyy_MM_dd-HH_mm_ss"
$outputFile = "Uptime_Kuma_Backup_$timestamp.json"

Write-Host "正在格式化 JSON 备份文件..." -ForegroundColor Green

try {
    # 读取原始文件
    $rawContent = Get-Content $inputFile -Raw
    
    # 解析 JSON 数组
    $jsonArray = $rawContent | ConvertFrom-Json
    
    # 提取 backup_data 字段并解析
    $backupDataString = $jsonArray[0].backup_data
    $backupData = $backupDataString | ConvertFrom-Json
    
    # 重新格式化为标准的 Uptime Kuma 备份格式
    $formattedBackup = @{
        version = $backupData.version
        datetime = $backupData.datetime
        monitor = $backupData.monitor
        notification = $backupData.notification
        tag = $backupData.tag
        setting = $backupData.setting
        docker_host = $backupData.docker_host
        proxy = $backupData.proxy
    }
    
    # 转换为格式化的 JSON
    $jsonOutput = $formattedBackup | ConvertTo-Json -Depth 10
    
    # 保存到文件
    $jsonOutput | Out-File -FilePath $outputFile -Encoding UTF8
    
    Write-Host "✅ 格式化完成！" -ForegroundColor Green
    Write-Host "📁 输出文件: $outputFile" -ForegroundColor Cyan
    
    # 显示统计信息
    Write-Host "`n=== 备份统计 ===" -ForegroundColor Magenta
    Write-Host "监控项目: $($formattedBackup.monitor.Count) 个" -ForegroundColor White
    Write-Host "通知设置: $($formattedBackup.notification.Count) 个" -ForegroundColor White
    Write-Host "标签: $($formattedBackup.tag.Count) 个" -ForegroundColor White
    Write-Host "设置项: $($formattedBackup.setting.Count) 个" -ForegroundColor White
    Write-Host "Docker 主机: $($formattedBackup.docker_host.Count) 个" -ForegroundColor White
    Write-Host "代理设置: $($formattedBackup.proxy.Count) 个" -ForegroundColor White
    
    # 显示部分监控项目
    Write-Host "`n=== 监控项目示例 ===" -ForegroundColor Magenta
    $formattedBackup.monitor | Select-Object -First 5 | ForEach-Object {
        $status = if ($_.active) { "✅" } else { "❌" }
        Write-Host "$status $($_.name) - $($_.url)" -ForegroundColor White
    }
    
    $fileSize = (Get-Item $outputFile).Length
    Write-Host "`n📊 文件大小: $fileSize 字节" -ForegroundColor Cyan
    
} catch {
    Write-Host "❌ 处理失败: $($_.Exception.Message)" -ForegroundColor Red
}
