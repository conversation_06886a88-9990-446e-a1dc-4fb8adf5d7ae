# Uptime Kuma 数据库导出为 JSON 格式脚本
# 作者: AI Assistant
# 日期: 2025-07-14

$sqlitePath = "$env:LOCALAPPDATA\Microsoft\WinGet\Packages\SQLite.SQLite_Microsoft.Winget.Source_8wekyb3d8bbwe\sqlite3.exe"
$dbPath = "data/kuma.db"
$outputPath = "Uptime_Kuma_Backup_$(Get-Date -Format 'yyyy_MM_dd-HH_mm_ss').json"

Write-Host "开始导出 Uptime Kuma 数据库..." -ForegroundColor Green

# 创建备份对象
$backup = @{
    version = "1.23.11"
    datetime = (Get-Date).ToString("yyyy-MM-dd HH:mm:ss")
}

Write-Host "导出监控项目..." -ForegroundColor Yellow
# 导出监控项目
$monitorQuery = "SELECT * FROM monitor;"
$monitorResult = & $sqlitePath $dbPath $monitorQuery
$monitors = @()

if ($monitorResult) {
    $lines = $monitorResult -split "`n"
    foreach ($line in $lines) {
        if ($line.Trim()) {
            $fields = $line -split '\|'
            if ($fields.Count -ge 3) {
                $monitor = @{
                    id = $fields[0]
                    name = $fields[1]
                    active = [bool][int]$fields[2]
                    user_id = $fields[3]
                    interval = $fields[4]
                    url = $fields[5]
                    type = $fields[6]
                    weight = $fields[7]
                    hostname = $fields[8]
                    port = $fields[9]
                    created_date = $fields[10]
                    keyword = $fields[11]
                    maxretries = $fields[12]
                    ignore_tls = [bool][int]$fields[13]
                    upside_down = [bool][int]$fields[14]
                    maxredirects = $fields[15]
                    accepted_statuscodes_json = $fields[16]
                    dns_resolve_type = $fields[17]
                    dns_resolve_server = $fields[18]
                    dns_last_result = $fields[19]
                    retry_interval = $fields[20]
                    push_token = $fields[21]
                    method = $fields[22]
                    body = $fields[23]
                    headers = $fields[24]
                    basic_auth_user = $fields[25]
                    basic_auth_pass = $fields[26]
                    timeout = $fields[27]
                    description = $fields[28]
                }
                $monitors += $monitor
            }
        }
    }
}

Write-Host "导出通知设置..." -ForegroundColor Yellow
# 导出通知设置
$notificationQuery = "SELECT * FROM notification;"
$notificationResult = & $sqlitePath $dbPath $notificationQuery
$notifications = @()

if ($notificationResult) {
    $lines = $notificationResult -split "`n"
    foreach ($line in $lines) {
        if ($line.Trim()) {
            $fields = $line -split '\|'
            if ($fields.Count -ge 3) {
                $notification = @{
                    id = $fields[0]
                    name = $fields[1]
                    active = [bool][int]$fields[2]
                    user_id = $fields[3]
                    is_default = [bool][int]$fields[4]
                    config = $fields[5]
                }
                $notifications += $notification
            }
        }
    }
}

Write-Host "导出标签..." -ForegroundColor Yellow
# 导出标签
$tagQuery = "SELECT * FROM tag;"
$tagResult = & $sqlitePath $dbPath $tagQuery
$tags = @()

if ($tagResult) {
    $lines = $tagResult -split "`n"
    foreach ($line in $lines) {
        if ($line.Trim()) {
            $fields = $line -split '\|'
            if ($fields.Count -ge 3) {
                $tag = @{
                    id = $fields[0]
                    name = $fields[1]
                    color = $fields[2]
                    created_date = $fields[3]
                }
                $tags += $tag
            }
        }
    }
}

Write-Host "导出设置..." -ForegroundColor Yellow
# 导出设置
$settingQuery = "SELECT * FROM setting;"
$settingResult = & $sqlitePath $dbPath $settingQuery
$settings = @()

if ($settingResult) {
    $lines = $settingResult -split "`n"
    foreach ($line in $lines) {
        if ($line.Trim()) {
            $fields = $line -split '\|'
            if ($fields.Count -ge 2) {
                $setting = @{
                    id = $fields[0]
                    key = $fields[1]
                    value = $fields[2]
                    type = $fields[3]
                }
                $settings += $setting
            }
        }
    }
}

# 组装最终的备份对象
$backup.monitor = $monitors
$backup.notification = $notifications
$backup.tag = $tags
$backup.setting = $settings

Write-Host "生成 JSON 文件..." -ForegroundColor Yellow
# 转换为 JSON 并保存
$jsonOutput = $backup | ConvertTo-Json -Depth 10 -Compress:$false

# 保存到文件
$jsonOutput | Out-File -FilePath $outputPath -Encoding UTF8

Write-Host "备份完成！文件保存为: $outputPath" -ForegroundColor Green
Write-Host "文件大小: $((Get-Item $outputPath).Length) 字节" -ForegroundColor Cyan

# 显示统计信息
Write-Host "`n=== 备份统计 ===" -ForegroundColor Magenta
Write-Host "监控项目: $($monitors.Count) 个" -ForegroundColor White
Write-Host "通知设置: $($notifications.Count) 个" -ForegroundColor White
Write-Host "标签: $($tags.Count) 个" -ForegroundColor White
Write-Host "设置项: $($settings.Count) 个" -ForegroundColor White
