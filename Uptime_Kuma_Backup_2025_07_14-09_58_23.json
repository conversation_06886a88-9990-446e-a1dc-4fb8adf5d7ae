{"version": "1.23.16", "notificationList": [], "monitorList": [{"id": 2, "name": "123", "description": null, "pathName": "huggingface / 123", "parent": 3, "childrenIDs": [], "url": "https://123.com", "method": "GET", "hostname": null, "port": null, "maxretries": 0, "weight": 2000, "active": true, "forceInactive": false, "type": "http", "timeout": 48, "interval": 60, "retryInterval": 60, "resendInterval": 0, "keyword": null, "invertKeyword": false, "expiryNotification": false, "ignoreTls": false, "upsideDown": false, "packetSize": 56, "maxredirects": 10, "accepted_statuscodes": ["200-299"], "dns_resolve_type": "A", "dns_resolve_server": "*******", "dns_last_result": null, "docker_container": "", "docker_host": null, "proxyId": null, "notificationIDList": {}, "tags": [], "maintenance": false, "mqttTopic": "", "mqttSuccessMessage": "", "databaseQuery": null, "authMethod": null, "grpcUrl": null, "grpcProtobuf": null, "grpcMethod": null, "grpcServiceName": null, "grpcEnableTls": false, "radiusCalledStationId": null, "radiusCallingStationId": null, "game": null, "gamedigGivenPortOnly": true, "httpBodyEncoding": "json", "jsonPath": null, "expectedValue": null, "kafkaProducerTopic": null, "kafkaProducerBrokers": [], "kafkaProducerSsl": false, "kafkaProducerAllowAutoTopicCreation": false, "kafkaProducerMessage": null, "screenshot": null, "headers": null, "body": null, "grpcBody": null, "grpcMetadata": null, "basic_auth_user": null, "basic_auth_pass": null, "oauth_client_id": null, "oauth_client_secret": null, "oauth_token_url": null, "oauth_scopes": null, "oauth_auth_method": "client_secret_basic", "pushToken": null, "databaseConnectionString": null, "radiusUsername": null, "radiusPassword": null, "radiusSecret": null, "mqttUsername": "", "mqttPassword": "", "authWorkstation": null, "authDomain": null, "tlsCa": null, "tlsCert": null, "tlsKey": null, "kafkaProducerSaslOptions": {"mechanism": "None"}, "includeSensitiveData": true}, {"id": 3, "name": "huggingface", "description": null, "pathName": "huggingface", "parent": null, "childrenIDs": [2], "url": "https://", "method": "GET", "hostname": null, "port": null, "maxretries": 0, "weight": 2000, "active": true, "forceInactive": false, "type": "group", "timeout": 48, "interval": 60, "retryInterval": 60, "resendInterval": 0, "keyword": null, "invertKeyword": false, "expiryNotification": false, "ignoreTls": false, "upsideDown": false, "packetSize": 56, "maxredirects": 10, "accepted_statuscodes": ["200-299"], "dns_resolve_type": "A", "dns_resolve_server": "*******", "dns_last_result": null, "docker_container": "", "docker_host": null, "proxyId": null, "notificationIDList": {}, "tags": [], "maintenance": false, "mqttTopic": "", "mqttSuccessMessage": "", "databaseQuery": null, "authMethod": null, "grpcUrl": null, "grpcProtobuf": null, "grpcMethod": null, "grpcServiceName": null, "grpcEnableTls": false, "radiusCalledStationId": null, "radiusCallingStationId": null, "game": null, "gamedigGivenPortOnly": true, "httpBodyEncoding": "json", "jsonPath": null, "expectedValue": null, "kafkaProducerTopic": null, "kafkaProducerBrokers": [], "kafkaProducerSsl": false, "kafkaProducerAllowAutoTopicCreation": false, "kafkaProducerMessage": null, "screenshot": null, "headers": null, "body": null, "grpcBody": null, "grpcMetadata": null, "basic_auth_user": null, "basic_auth_pass": null, "oauth_client_id": null, "oauth_client_secret": null, "oauth_token_url": null, "oauth_scopes": null, "oauth_auth_method": "client_secret_basic", "pushToken": null, "databaseConnectionString": null, "radiusUsername": null, "radiusPassword": null, "radiusSecret": null, "mqttUsername": "", "mqttPassword": "", "authWorkstation": null, "authDomain": null, "tlsCa": null, "tlsCert": null, "tlsKey": null, "kafkaProducerSaslOptions": {"mechanism": "None"}, "includeSensitiveData": true}]}