# 创建标准 Uptime Kuma JSON 备份文件
$sqlitePath = "$env:LOCALAPPDATA\Microsoft\WinGet\Packages\SQLite.SQLite_Microsoft.Winget.Source_8wekyb3d8bbwe\sqlite3.exe"
$dbPath = "data/kuma.db"
$timestamp = Get-Date -Format "yyyy_MM_dd-HH_mm_ss"
$outputFile = "Uptime_Kuma_Backup_$timestamp.json"

Write-Host "正在创建 Uptime Kuma JSON 备份..." -ForegroundColor Green

# 使用 SQLite 的 JSON 功能直接导出
$exportQuery = @"
.mode json
.output $outputFile
SELECT json_object(
    'version', '1.23.11',
    'datetime', datetime('now'),
    'monitor', (
        SELECT json_group_array(
            json_object(
                'id', id,
                'name', name,
                'active', CASE WHEN active = 1 THEN json('true') ELSE json('false') END,
                'user_id', user_id,
                'interval', interval,
                'url', url,
                'type', type,
                'weight', weight,
                'hostname', hostname,
                'port', port,
                'created_date', created_date,
                'keyword', keyword,
                'maxretries', maxretries,
                'ignore_tls', CASE WHEN ignore_tls = 1 THEN json('true') ELSE json('false') END,
                'upside_down', CASE WHEN upside_down = 1 THEN json('true') ELSE json('false') END,
                'maxredirects', maxredirects,
                'accepted_statuscodes_json', accepted_statuscodes_json,
                'dns_resolve_type', dns_resolve_type,
                'dns_resolve_server', dns_resolve_server,
                'dns_last_result', dns_last_result,
                'retry_interval', retry_interval,
                'push_token', push_token,
                'method', method,
                'body', body,
                'headers', headers,
                'basic_auth_user', basic_auth_user,
                'basic_auth_pass', basic_auth_pass,
                'timeout', timeout,
                'description', description,
                'proxy_id', proxy_id,
                'expiry_notification', CASE WHEN expiry_notification = 1 THEN json('true') ELSE json('false') END,
                'mqtt_topic', mqtt_topic,
                'mqtt_success_message', mqtt_success_message,
                'mqtt_username', mqtt_username,
                'mqtt_password', mqtt_password,
                'database_connection_string', database_connection_string,
                'database_query', database_query,
                'auth_method', auth_method,
                'auth_domain', auth_domain,
                'auth_workstation', auth_workstation,
                'grpc_url', grpc_url,
                'grpc_protobuf', grpc_protobuf,
                'grpc_body', grpc_body,
                'grpc_metadata', grpc_metadata,
                'grpc_method', grpc_method,
                'grpc_service_name', grpc_service_name,
                'grpc_enable_tls', CASE WHEN grpc_enable_tls = 1 THEN json('true') ELSE json('false') END,
                'radius_username', radius_username,
                'radius_password', radius_password,
                'radius_calling_station_id', radius_calling_station_id,
                'radius_called_station_id', radius_called_station_id,
                'radius_secret', radius_secret,
                'resend_interval', resend_interval,
                'packet_size', packet_size,
                'game', game,
                'http_body_encoding', http_body_encoding,
                'tls_ca', tls_ca,
                'tls_cert', tls_cert,
                'tls_key', tls_key,
                'parent', parent,
                'invert_keyword', CASE WHEN invert_keyword = 1 THEN json('true') ELSE json('false') END,
                'json_path', json_path,
                'expected_value', expected_value,
                'kafka_producer_topic', kafka_producer_topic,
                'kafka_producer_brokers', kafka_producer_brokers,
                'kafka_producer_sasl_options', kafka_producer_sasl_options,
                'kafka_producer_message', kafka_producer_message,
                'oauth_client_id', oauth_client_id,
                'oauth_client_secret', oauth_client_secret,
                'oauth_token_url', oauth_token_url,
                'oauth_scopes', oauth_scopes,
                'oauth_auth_method', oauth_auth_method,
                'gamedig_given_port_only', CASE WHEN gamedig_given_port_only = 1 THEN json('true') ELSE json('false') END,
                'kafka_producer_ssl', CASE WHEN kafka_producer_ssl = 1 THEN json('true') ELSE json('false') END,
                'kafka_producer_allow_auto_topic_creation', CASE WHEN kafka_producer_allow_auto_topic_creation = 1 THEN json('true') ELSE json('false') END
            )
        ) FROM monitor
    ),
    'notification', (
        SELECT json_group_array(
            json_object(
                'id', id,
                'name', name,
                'active', CASE WHEN active = 1 THEN json('true') ELSE json('false') END,
                'user_id', user_id,
                'is_default', CASE WHEN is_default = 1 THEN json('true') ELSE json('false') END,
                'config', json(config)
            )
        ) FROM notification
    ),
    'tag', (
        SELECT json_group_array(
            json_object(
                'id', id,
                'name', name,
                'color', color,
                'created_date', created_date
            )
        ) FROM tag
    ),
    'setting', (
        SELECT json_group_array(
            json_object(
                'id', id,
                'key', key,
                'value', value,
                'type', type
            )
        ) FROM setting
    ),
    'docker_host', (
        SELECT json_group_array(
            json_object(
                'id', id,
                'user_id', user_id,
                'docker_daemon', docker_daemon,
                'docker_type', docker_type,
                'name', name
            )
        ) FROM docker_host
    ),
    'proxy', (
        SELECT json_group_array(
            json_object(
                'id', id,
                'user_id', user_id,
                'protocol', protocol,
                'host', host,
                'port', port,
                'auth', CASE WHEN auth = 1 THEN json('true') ELSE json('false') END,
                'username', username,
                'password', password,
                'active', CASE WHEN active = 1 THEN json('true') ELSE json('false') END,
                'default', CASE WHEN "default" = 1 THEN json('true') ELSE json('false') END,
                'created_date', created_date
            )
        ) FROM proxy
    )
) as backup_data;
"@

# 将查询写入临时文件
$tempQueryFile = "temp_export_query.sql"
$exportQuery | Out-File -FilePath $tempQueryFile -Encoding UTF8

# 执行 SQLite 查询
& $sqlitePath $dbPath ".read $tempQueryFile"

# 清理临时文件
Remove-Item $tempQueryFile -ErrorAction SilentlyContinue

if (Test-Path $outputFile) {
    $fileSize = (Get-Item $outputFile).Length
    Write-Host "✅ 备份完成！" -ForegroundColor Green
    Write-Host "📁 文件名: $outputFile" -ForegroundColor Cyan
    Write-Host "📊 文件大小: $fileSize 字节" -ForegroundColor Cyan
    
    # 验证 JSON 格式
    try {
        $jsonContent = Get-Content $outputFile -Raw | ConvertFrom-Json
        Write-Host "✅ JSON 格式验证通过" -ForegroundColor Green
        
        # 显示统计信息
        Write-Host "`n=== 备份统计 ===" -ForegroundColor Magenta
        Write-Host "监控项目: $($jsonContent.monitor.Count) 个" -ForegroundColor White
        Write-Host "通知设置: $($jsonContent.notification.Count) 个" -ForegroundColor White
        Write-Host "标签: $($jsonContent.tag.Count) 个" -ForegroundColor White
        Write-Host "设置项: $($jsonContent.setting.Count) 个" -ForegroundColor White
        Write-Host "Docker 主机: $($jsonContent.docker_host.Count) 个" -ForegroundColor White
        Write-Host "代理设置: $($jsonContent.proxy.Count) 个" -ForegroundColor White
    }
    catch {
        Write-Host "⚠️ JSON 格式验证失败: $($_.Exception.Message)" -ForegroundColor Yellow
    }
} else {
    Write-Host "❌ 备份失败！未找到输出文件" -ForegroundColor Red
}
